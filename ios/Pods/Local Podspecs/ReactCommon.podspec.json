{"name": "ReactCommon", "module_name": "ReactCommon", "version": "0.76.6", "summary": "-", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.76.6"}, "header_dir": "ReactCommon", "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/RCT-Folly\" \"$(PODS_ROOT)/DoubleConversion\" \"$(PODS_ROOT)/fmt/include\" \"$(PODS_ROOT)/Headers/Private/React-Core\"", "USE_HEADERMAP": "YES", "DEFINES_MODULE": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "GCC_WARN_PEDANTIC": "YES"}, "subspecs": [{"name": "turbomodule", "dependencies": {"React-callinvoker": ["0.76.6"], "React-perflogger": ["0.76.6"], "React-cxxreact": ["0.76.6"], "React-jsi": ["0.76.6"], "RCT-Folly": ["2024.01.01.00"], "React-logger": ["0.76.6"], "DoubleConversion": [], "fmt": ["9.1.0"], "glog": []}, "subspecs": [{"name": "bridging", "dependencies": {"React-jsi": ["0.76.6"]}, "source_files": "react/bridging/**/*.{cpp,h}", "exclude_files": "react/bridging/tests", "header_dir": "react/bridging", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)/ReactCommon\" \"$(PODS_ROOT)/RCT-Folly\""}}, {"name": "core", "source_files": "react/nativemodule/core/ReactCommon/**/*.{cpp,h}", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)/ReactCommon\" \"$(PODS_CONFIGURATION_BUILD_DIR)/React-debug/React_debug.framework/Headers\" \"$(PODS_CONFIGURATION_BUILD_DIR)/React-debug/React_featureflags.framework/Headers\" \"$(PODS_CONFIGURATION_BUILD_DIR)/React-utils/React_utils.framework/Headers\""}, "dependencies": {"React-debug": ["0.76.6"], "React-featureflags": ["0.76.6"], "React-utils": ["0.76.6"]}}]}]}