{"name": "RNCClipboard", "version": "1.16.2", "summary": "React Native Clipboard API for macOS, iOS, Android, and Windows", "license": "MIT", "authors": "<PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/react-native-clipboard/clipboard#readme", "source": {"git": "https://github.com/react-native-clipboard/clipboard", "tag": "v1.16.2"}, "ios": {"source_files": "ios/**/*.{h,m,mm}"}, "osx": {"source_files": "macos/**/*.{h,m,mm}"}, "visionos": {"source_files": "ios/**/*.{h,m,mm}"}, "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/boost-for-react-native\" \"$(PODS_ROOT)/RCT-Folly\" \"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/Headers/Private/Yoga\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 -DF<PERSON><PERSON>Y_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32"}, "platforms": {"ios": "11.0", "tvos": "11.0", "osx": "10.14", "visionos": "1.0"}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON><PERSON>Y_MOBILE=1 -<PERSON><PERSON><PERSON><PERSON>Y_USE_LIBCPP=1 -Wno-comma -Wno-shorten-64-to-32 -DRCT_NEW_ARCH_ENABLED  -DRCT_NEW_ARCH_ENABLED=1 -D<PERSON><PERSON><PERSON>Y_NO_CONFIG -<PERSON><PERSON><PERSON><PERSON>Y_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DF<PERSON><PERSON>Y_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "dependencies": {"React-Core": [], "RCT-Folly": ["2024.01.01.00"], "glog": [], "React-RCTFabric": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "DoubleConversion": [], "React-jsi": []}}