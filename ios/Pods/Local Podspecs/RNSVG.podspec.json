{"name": "RNSVG", "version": "15.11.2", "summary": "SVG library for react-native", "license": "MIT", "homepage": "https://github.com/react-native-community/react-native-svg", "authors": "<PERSON><PERSON><PERSON><PERSON>", "source": {"git": "https://github.com/react-native-community/react-native-svg.git", "tag": "v15.11.2"}, "source_files": "apple/**/*.{h,m,mm}", "ios": {"exclude_files": "**/*.macos.{h,m,mm}", "resource_bundles": {"RNSVGFilters": ["apple/**/*.iphoneos.metallib"]}}, "tvos": {"exclude_files": "**/*.macos.{h,m,mm}", "resource_bundles": {"RNSVGFilters": ["apple/**/*.appletvos.metallib"]}}, "visionos": {"exclude_files": "**/*.macos.{h,m,mm}", "resource_bundles": {"RNSVGFilters": ["apple/**/*.xros.metallib"]}}, "osx": {"exclude_files": "**/*.ios.{h,m,mm}", "resource_bundles": {"RNSVGFilters": ["apple/**/*.macosx.metallib"]}}, "requires_arc": true, "platforms": {"osx": "10.14", "ios": "12.4", "tvos": "12.4", "visionos": "1.0"}, "xcconfig": {"OTHER_CFLAGS": "$(inherited) -DREACT_NATIVE_MINOR_VERSION=76"}, "compiler_flags": "-DRCT_NEW_ARCH_ENABLED=1 -D<PERSON><PERSON><PERSON><PERSON>_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DF<PERSON>LY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "dependencies": {"React-Core": [], "RCT-Folly": ["2024.01.01.00"], "glog": [], "React-RCTFabric": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "DoubleConversion": [], "React-jsi": []}, "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/Headers/Private/Yoga\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 -DF<PERSON><PERSON>Y_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32"}, "subspecs": [{"name": "common", "source_files": "common/cpp/**/*.{cpp,h}", "header_dir": "rnsvg", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)/common/cpp\""}}]}