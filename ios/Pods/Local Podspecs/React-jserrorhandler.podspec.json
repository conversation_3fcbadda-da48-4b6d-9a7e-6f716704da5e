{"name": "React-jserror<PERSON>ler", "version": "0.76.6", "summary": "-", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.76.6"}, "header_dir": "jserror<PERSON><PERSON>", "source_files": "Js<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.{cpp,h}", "pod_target_xcconfig": {"USE_HEADERMAP": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "HEADER_SEARCH_PATHS": "\"${PODS_CONFIGURATION_BUILD_DIR}/React-debug/React_debug.framework/Headers\""}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation", "dependencies": {"RCT-Folly/Fabric": ["2024.01.01.00"], "React-jsi": [], "React-cxxreact": [], "glog": [], "React-debug": []}}