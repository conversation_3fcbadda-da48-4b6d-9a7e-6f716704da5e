{"name": "RNShare", "version": "10.2.1", "summary": "Social share, sending simple data to other apps.", "homepage": "https://react-native-share.github.io/react-native-share/", "license": "MIT", "authors": {"Esteban Fuentealba": "<EMAIL>"}, "platforms": {"ios": "8.0"}, "source": {"git": "https://github.com/react-native-community/react-native-share.git", "tag": "v10.2.1"}, "source_files": "ios/**/*.{h,m,mm}", "dependencies": {"React-Core": [], "React-Codegen": [], "React-RCTFabric": [], "RCT-Folly": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/core": []}, "ios": {"weak_frameworks": "LinkPresentation"}, "compiler_flags": "-DFOLLY_NO_CONFIG -DFOLLY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -Wno-comma -Wno-shorten-64-to-32 -DRCT_NEW_ARCH_ENABLED=1", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\"", "OTHER_CPLUSPLUSFLAGS": "-DFOLLY_NO_CONFIG -DFOLLY_MOBILE=1 -DFOLLY_USE_LIBCPP=1", "CLANG_CXX_LANGUAGE_STANDARD": "c++17"}}