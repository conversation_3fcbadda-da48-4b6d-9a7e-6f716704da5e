{"name": "react-native-safe-area-context", "version": "4.14.1", "summary": "A flexible way to handle safe area, also works on Android and web.", "license": "MIT", "authors": "<PERSON><PERSON> <janic<PERSON>ples<PERSON>@gmail.com>", "homepage": "https://github.com/th3rdwave/react-native-safe-area-context#readme", "platforms": {"ios": "12.4", "osx": "10.15", "tvos": "12.4", "visionos": "1.0"}, "source": {"git": "https://github.com/th3rdwave/react-native-safe-area-context.git", "tag": "v4.14.1"}, "source_files": "ios/**/*.{h,m,mm}", "exclude_files": "ios/Fabric", "dependencies": {"React-Core": [], "RCT-Folly": ["2024.01.01.00"], "glog": [], "React-RCTFabric": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "DoubleConversion": [], "React-jsi": []}, "compiler_flags": "-DRCT_NEW_ARCH_ENABLED=1 -D<PERSON><PERSON><PERSON><PERSON>_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DF<PERSON>LY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/Headers/Private/Yoga\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 -DF<PERSON><PERSON>Y_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32"}, "subspecs": [{"name": "common", "source_files": "common/cpp/**/*.{cpp,h}", "header_dir": "react/renderer/components/safeareacontext", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)/common/cpp\""}}, {"name": "fabric", "dependencies": {"react-native-safe-area-context/common": []}, "source_files": "ios/Fabric/**/*.{h,m,mm}", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)/common/cpp\""}}]}