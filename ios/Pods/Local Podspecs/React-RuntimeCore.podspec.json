{"name": "React-RuntimeCore", "version": "0.76.6", "summary": "The React Native Runtime.", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.76.6"}, "source_files": ["*.{cpp,h}", "nativeviewconfig/*.{cpp,h}"], "exclude_files": ["iostests/*", "tests/**/*.{cpp,h}"], "header_dir": "react/runtime", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/Headers/Private/React-Core\" \"${PODS_TARGET_SRCROOT}/../..\"", "USE_HEADERMAP": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "GCC_WARN_PEDANTIC": "YES"}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation", "dependencies": {"RCT-Folly/Fabric": ["2024.01.01.00"], "React-jsiexecutor": [], "React-cxxreact": [], "React-runtimeexecutor": [], "glog": [], "React-jsi": [], "React-jserrorhandler": [], "React-performancetimeline": [], "React-runtimescheduler": [], "React-utils": [], "React-featureflags": [], "React-jsc": [], "React-jsinspector": []}}