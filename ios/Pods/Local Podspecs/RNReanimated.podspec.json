{"name": "RNReanimated", "version": "3.17.5", "summary": "More powerful alternative to Animated library for React Native.", "description": "RNReanimated", "homepage": "https://github.com/software-mansion/react-native-reanimated", "license": "MIT", "authors": {"author": "<EMAIL>"}, "platforms": {"ios": "13.4", "tvos": "9.0", "osx": "10.14", "visionos": "1.0"}, "source": {"git": "https://github.com/software-mansion/react-native-reanimated.git", "tag": "3.17.5"}, "pod_target_xcconfig": {"USE_HEADERMAP": "YES", "DEFINES_MODULE": "YES", "HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)/ReactCommon\" \"$(PODS_TARGET_SRCROOT)\" \"$(PODS_ROOT)/RCT-Folly\" \"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/boost-for-react-native\" \"$(PODS_ROOT)/DoubleConversion\" \"$(PODS_ROOT)/Headers/Private/React-Core\" \"$(PODS_ROOT)/Headers/Private/Yoga\" \"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/Headers/Private/Yoga\"", "FRAMEWORK_SEARCH_PATHS": "\"${PODS_CONFIGURATION_BUILD_DIR}/React-hermes\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "GCC_PREPROCESSOR_DEFINITIONS[config=Debug]": "$(inherited) HERMES_ENABLE_DEBUGGER=1", "GCC_PREPROCESSOR_DEFINITIONS[config=Release]": "$(inherited) NDEBUG=1", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 -DF<PERSON><PERSON>Y_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32"}, "compiler_flags": "-DFOLLY_NO_CONFIG -D<PERSON><PERSON><PERSON>Y_MOBILE=1 -<PERSON><PERSON><PERSON><PERSON>Y_USE_LIBCPP=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation  -DRCT_NEW_ARCH_ENABLED=1 -DF<PERSON>LY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DF<PERSON>LY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/boost-for-react-native\" \"$(PODS_ROOT)/glog\" \"$(PODS_ROOT)/RCT-Folly\" \"$(PODS_ROOT)/Headers/Public/React-hermes\" \"$(PODS_ROOT)/Headers/Public/hermes-engine\" \"$(PODS_ROOT)/../../node_modules/react-native/ReactCommon\" \"$(PODS_ROOT)/../../node_modules/react-native-reanimated/apple\" \"$(PODS_ROOT)/../../node_modules/react-native-reanimated/Common/cpp\"", "OTHER_CFLAGS": "$(inherited) -DF<PERSON>LY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -Wno-comma -Wno-shorten-64-to-32 -DRCT_NEW_ARCH_ENABLED  -DREACT_NATIVE_MINOR_VERSION=76 -DREANIMATED_VERSION=3.17.5  "}, "requires_arc": true, "dependencies": {"React-Core": [], "RCT-Folly": ["2024.01.01.00"], "glog": [], "React-RCTFabric": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "DoubleConversion": [], "React-jsi": []}, "subspecs": [{"name": "worklets", "source_files": "Common/cpp/worklets/**/*.{cpp,h}", "header_dir": "worklets", "header_mappings_dir": "Common/cpp/worklets", "subspecs": [{"name": "apple", "source_files": "apple/worklets/**/*.{mm,h,m}", "header_dir": "worklets", "header_mappings_dir": "apple/worklets"}]}, {"name": "reanimated", "source_files": "Common/cpp/reanimated/**/*.{cpp,h}", "header_dir": "reanimated", "header_mappings_dir": "Common/cpp/reanimated", "subspecs": [{"name": "apple", "source_files": "apple/reanimated/**/*.{mm,h,m}", "header_dir": "reanimated", "header_mappings_dir": "apple/reanimated"}]}]}