{"name": "React-rendererdebug", "version": "0.76.6", "summary": "-", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.76.6"}, "source_files": "**/*.{cpp,h,mm}", "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "header_dir": "react/renderer/debug", "exclude_files": "tests", "pod_target_xcconfig": {"CLANG_CXX_LANGUAGE_STANDARD": "c++20", "HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/RCT-Folly\" \"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/RCT-Folly\" \"$(PODS_ROOT)/DoubleConversion\" \"$(PODS_ROOT)/fmt/include\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-debug/React_debug.framework/Headers\"", "DEFINES_MODULE": "YES"}, "dependencies": {"RCT-Folly": ["2024.01.01.00"], "DoubleConversion": [], "fmt": ["9.1.0"], "React-debug": []}}