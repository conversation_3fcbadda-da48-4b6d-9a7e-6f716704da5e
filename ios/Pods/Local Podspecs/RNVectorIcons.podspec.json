{"name": "RNVectorIcons", "version": "10.2.0", "summary": "Customizable Icons for React Native with support for NavBar/TabBar, image source and full styling.", "description": "Customizable Icons for React Native with support for NavBar/TabBar, image source and full styling.", "homepage": "https://github.com/oblador/react-native-vector-icons", "license": "MIT", "authors": {"Joel Arvidsson": "<EMAIL>"}, "platforms": {"ios": "12.0", "tvos": "9.0", "visionos": "1.0"}, "source": {"git": "git://github.com/oblador/react-native-vector-icons.git", "tag": "v10.2.0"}, "source_files": "RNVectorIconsManager/**/*.{h,m,mm,swift}", "resources": "Fonts/*.ttf", "preserve_paths": "**/*.js", "compiler_flags": "-DRCT_NEW_ARCH_ENABLED=1 -D<PERSON><PERSON><PERSON><PERSON>_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DF<PERSON>LY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "dependencies": {"React-Core": [], "RCT-Folly": ["2024.01.01.00"], "glog": [], "React-RCTFabric": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "DoubleConversion": [], "React-jsi": []}, "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/Headers/Private/Yoga\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 -DF<PERSON><PERSON>Y_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32"}}