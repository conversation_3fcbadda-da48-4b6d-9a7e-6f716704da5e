{"name": "RCTTypeSafety", "version": "0.76.6", "summary": "-", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.76.6"}, "source_files": "**/*.{c,h,m,mm,cpp}", "header_dir": "RCTTypeSafety", "pod_target_xcconfig": {"USE_HEADERMAP": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "HEADER_SEARCH_PATHS": "\"$(PODS_TARGET_SRCROOT)/Libraries/TypeSafety\""}, "dependencies": {"FBLazyVector": ["0.76.6"], "RCTRequired": ["0.76.6"], "React-Core": ["0.76.6"]}}